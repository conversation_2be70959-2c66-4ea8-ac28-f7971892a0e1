import React from 'react';
import { Descriptions, Tooltip } from 'antd';
import { connect } from 'dva';
import { renderDescItem } from '../const';
import styles from '../DetailInfo.module.less';

const fields = [
  {
    label: '投标资料名称',
    key: 'knowledgeName',
    span: 2,
  },
  {
    label: '投标资料编号',
    key: 'knowledgeNbr',
    optionKey: '',
  },
  {
    label: '归属地市',
    key: 'regionName',
  },
  {
    label: '归属区县',
    key: 'areaName',
  },
  {
    label: '投标模板类型',
    key: 'knowledgeTypeName',
  },
  {
    label: '9大行业',
    key: 'industryTypeName',
  },
  {
    label: '一级行业',
    key: 'industryFirstTypeName',
  },
  {
    label: '二级行业',
    key: 'industrySubTypeName',
  },
  {
    label: '所属产品',
    key: 'prodTypeName',
  },
  {
    label: '资料联系人姓名',
    key: 'contactName',
  },
  {
    label: '资料联系人电话',
    key: 'contactPhone',
  },
  {
    label: '资料联系人邮箱',
    key: 'contactEmail',
  },
  {
    label: '资料维护人姓名',
    key: 'createStaffName',
  },
  {
    label: '资料维护人电话',
    key: 'defandTel',
  },
  {
    label: '资料维护人邮箱',
    key: 'defandMail',
  },
  {
    label: '生效时间(上架)',
    key: 'shelfTime',
  },
  {
    label: '失效时间(下架)',
    key: 'shelvesTime',
    span: 1,
  },
  {
    label: '归属人单位',
    key: 'userDepartName',
    span: 1,
  },
  {
    label: '资料关键词(标签)',
    key: 'knowledgeKeyword',
    span: 3,
  },
  {
    label: '资料简介',
    key: 'knowledgeDesc',
    span: 3,
  },
];

/**
 * 投标库基本信息组件
 * @param {props}} props
 * @returns
 */
const BidLibDetailComp = props => {
  const { detail, colFields } = props;

  return (
    <div>
      <Descriptions>
        {fields?.map(row => {
          const text = renderDescItem({ ...row, detail, colFields });
          return (
            <Descriptions.Item label={row.label} span={row.span} key={row.key}>
              <Tooltip title={text} placement="bottomLeft" mouseEnterDelay={1}>
                <div className={`${row.key === 'knowledgeName' ? styles.description : null}`}>{text}</div>
              </Tooltip>
            </Descriptions.Item>
          );
        })}
      </Descriptions>
    </div>
  );
};

export default connect(({ librarys }) => ({ ...librarys }))(BidLibDetailComp);
